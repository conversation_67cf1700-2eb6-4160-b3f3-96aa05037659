import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './hooks/use-auth'
import { Toaster } from './components/ui/toaster'
import { NotificationProvider } from './components/providers/notification-provider'
import TitleBar from './components/TitleBar'
import LoginPage from './pages/LoginPage'
import DashboardLayout from './components/layout/DashboardLayout'
import Dashboard from './pages/Dashboard'
import Students from './pages/Students'
import Analytics from './pages/Analytics'
import Settings from './pages/Settings'
import Users from './pages/Users'
import StudentRegistration from './pages/StudentRegistration'
import MyChildren from './pages/MyChildren'
import AcademicFees from './pages/AcademicFees'
import TeacherAcademicFees from './pages/TeacherAcademicFees'
import Subjects from './pages/Subjects'
import Results from './pages/Results'
import ViewResults from './pages/ViewResults'
import ProtectedRoute from './components/auth/ProtectedRoute'
import { useEffect, useState } from 'react'
import SplashScreen from './components/SplashScreen'
import LoadingTransition from './components/LoadingTransition'
import ErrorBoundary from './components/ErrorBoundary'
import { useAuth } from './hooks/use-auth'
import { preloadLogo } from './assets'
import { getOfflineManager } from './lib/offline-manager'
import UpdateNotification from './components/UpdateNotification'
import NetworkStatusManager from './components/NetworkStatusManager'

// Initial route component to handle authentication redirect
function InitialRoute() {
  const { user, isLoading } = useAuth()

  if (isLoading) {
    return <LoadingTransition isVisible={true} />
  }

  if (user) {
    return <Navigate to="/dashboard" replace />
  }

  return <Navigate to="/login" replace />
}

// Main App component that handles splash screen and auth
function AppContent() {
  const [showSplash, setShowSplash] = useState(true)

  const handleSplashComplete = () => {
    setShowSplash(false)
  }

  if (showSplash) {
    return <SplashScreen onComplete={handleSplashComplete} />
  }

  return (
    <Router>
      {/* Network Status Manager - handles network change notifications */}
      <NetworkStatusManager />

      <Routes>
        {/* Login route - no title bar */}
        <Route path="/login" element={<LoginPage />} />

        {/* All other routes use the dashboard layout with title bar */}
        <Route path="/*" element={
          <div className="h-screen bg-background text-foreground flex flex-col overflow-hidden">
            {/* Fixed Custom Title Bar */}
            <div className="flex-shrink-0">
              <TitleBar />
            </div>

            {/* Main Content - Scrollable */}
            <div className="flex-1 overflow-hidden">
              <Routes>
                      <Route
                        path="/dashboard"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Dashboard />} />
                      </Route>

                      <Route
                        path="/students"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Students />} />
                      </Route>

                      <Route
                        path="/analytics"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Analytics />} />
                      </Route>

                      <Route
                        path="/settings"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Settings />} />
                      </Route>

                      <Route
                        path="/users"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Users />} />
                      </Route>

                      <Route
                        path="/student-registration"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<StudentRegistration />} />
                      </Route>

                      <Route
                        path="/my-children"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<MyChildren />} />
                      </Route>

                      <Route
                        path="/academic-fees"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<AcademicFees />} />
                      </Route>

                      <Route
                        path="/teacher-academic-fees"
                        element={
                          <ProtectedRoute>
                            <ErrorBoundary>
                              <DashboardLayout />
                            </ErrorBoundary>
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<TeacherAcademicFees />} />
                      </Route>

                      <Route
                        path="/subjects"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Subjects />} />
                      </Route>

                      <Route
                        path="/results"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<Results />} />
                      </Route>

                      <Route
                        path="/view-results"
                        element={
                          <ProtectedRoute>
                            <DashboardLayout />
                          </ProtectedRoute>
                        }
                      >
                        <Route index element={<ViewResults />} />
                      </Route>

                      {/* Default route - redirect to login if not authenticated, dashboard if authenticated */}
                      <Route path="/" element={<InitialRoute />} />
                      <Route path="*" element={<Navigate to="/" replace />} />
                    </Routes>

                    <Toaster />
                    <UpdateNotification />
                  </div>
                </div>
              } />
            </Routes>
      </Router>
  )
}

export default function App() {
  useEffect(() => {
    // Preload assets
    preloadLogo()

    // Initialize offline manager
    const initializeOfflineManager = async () => {
      try {
        console.log('🚀 Initializing offline manager...');
        const offlineManager = getOfflineManager();
        await offlineManager.initialize();
        console.log('✅ Offline manager initialized successfully');
      } catch (error) {
        console.error('❌ Failed to initialize offline manager:', error);
        // App can still work without offline functionality
        // Show a warning to the user but don't crash the app
      }
    };

    initializeOfflineManager();

    // Cleanup on app unmount
    return () => {
      const offlineManager = getOfflineManager();
      offlineManager.shutdown().catch(console.error);
    };
  }, [])

  return (
    <ErrorBoundary>
      <AuthProvider>
        <NotificationProvider>
          <AppContent />
        </NotificationProvider>
      </AuthProvider>
    </ErrorBoundary>
  )
}