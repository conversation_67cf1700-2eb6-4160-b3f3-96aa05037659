import { useState, useEffect } from 'react'
import { useAuth } from '../hooks/use-auth'
import ContextMenu from './ContextMenu'
import { getLogoPath } from '../assets'



export default function TitleBar() {
  const { user } = useAuth()
  const [isMaximized, setIsMaximized] = useState(false)
  const [currentTime, setCurrentTime] = useState(new Date())
  const [contextMenu, setContextMenu] = useState<{ x: number; y: number } | null>(null)

  useEffect(() => {
    // Check if running in Electron
    if (typeof window !== 'undefined' && window.electronAPI) {
      // Get initial maximized state
      window.electronAPI.isWindowMaximized().then(setIsMaximized)

      // Listen for window state changes
      window.electronAPI.onWindowMaximized(() => setIsMaximized(true))
      window.electronAPI.onWindowUnmaximized(() => setIsMaximized(false))
    }

    // Update time every second
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const handleMinimize = () => {
    if (window.electronAPI) {
      window.electronAPI.minimizeWindow()
    }
  }

  const handleMaximize = () => {
    if (window.electronAPI) {
      window.electronAPI.maximizeWindow()
    }
  }

  const handleClose = () => {
    if (window.electronAPI) {
      window.electronAPI.closeWindow()
    }
  }

  const handleContextMenu = (event: React.MouseEvent) => {
    event.preventDefault()
    setContextMenu({ x: event.clientX, y: event.clientY })
  }

  const handleMenuClick = (event: React.MouseEvent) => {
    const rect = (event.target as HTMLElement).getBoundingClientRect()
    setContextMenu({
      x: rect.left + rect.width / 2,
      y: rect.bottom + 5
    })
  }

  // Don't render if not in Electron
  if (typeof window === 'undefined' || !window.electronAPI) {
    return null
  }

  return (
    <div className="flex items-center justify-between h-16 bg-gradient-to-r from-slate-900 via-slate-800 to-slate-900 border-b border-slate-700/50 px-4 select-none" style={{ WebkitAppRegion: 'drag' } as any}>
      {/* Left Section - App Info */}
      <div className="flex items-center space-x-4">
        <div className="flex items-center space-x-3">
          {/* App Icon */}
          <div className="w-8 h-8 rounded-lg overflow-hidden shadow-lg">
            <img
              src={getLogoPath()}
              alt="Maggie Preparatory School Logo"
              className="w-full h-full object-cover"
            />
          </div>
          
          {/* App Title */}
          <div>
            <h1 className="text-white font-semibold text-sm">Maggie Preparatory School</h1>
            <p className="text-slate-400 text-xs">Management System</p>
          </div>
        </div>
      </div>

      {/* Center Section - User Info & Time */}
      <div className="flex items-center space-x-6">
        {user && (
          <div className="flex items-center space-x-3">
            {/* User Avatar */}
            <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-xs font-medium">
                {user.name?.split(' ').map(n => n[0]).join('').toUpperCase()}
              </span>
            </div>
            
            {/* User Info */}
            <div className="text-right">
              <p className="text-white text-xs font-medium">{user.name}</p>
              <p className="text-slate-400 text-xs capitalize">{user.role}</p>
            </div>
          </div>
        )}

        {/* Current Time */}
        <div className="text-right">
          <p className="text-white text-xs font-medium">
            {currentTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </p>
          <p className="text-slate-400 text-xs">
            {currentTime.toLocaleDateString([], { month: 'short', day: 'numeric' })}
          </p>
        </div>
      </div>

      {/* Right Section - Menu & Window Controls */}
      <div className="flex items-center space-x-2" style={{ WebkitAppRegion: 'no-drag' } as any}>
        {/* Menu Button - Only show when authenticated */}
        {user && (
          <>
            <button
              type="button"
              onClick={handleMenuClick}
              onContextMenu={handleContextMenu}
              className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-slate-700/50 transition-colors duration-200 group"
              title="Menu"
            >
              <svg className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>

            <div className="w-px h-6 bg-slate-600"></div>
          </>
        )}

        {/* Minimize Button */}
        <button
          type="button"
          onClick={handleMinimize}
          className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-slate-700/50 transition-colors duration-200 group"
          title="Minimize"
        >
          <svg className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
          </svg>
        </button>

        {/* Maximize/Restore Button */}
        <button
          type="button"
          onClick={handleMaximize}
          className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-slate-700/50 transition-colors duration-200 group"
          title={isMaximized ? "Restore" : "Maximize"}
        >
          {isMaximized ? (
            <svg className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 9a2 2 0 002 2h8a2 2 0 002-2l-2-9m-6 0V7" />
            </svg>
          ) : (
            <svg className="w-4 h-4 text-slate-400 group-hover:text-white transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4a2 2 0 012-2h4a2 2 0 012 2v4M4 8h8M4 8v8a2 2 0 002 2h4a2 2 0 002-2V8m4-4v4m0 0v8a2 2 0 002 2h4a2 2 0 002-2V8m0 0h4" />
            </svg>
          )}
        </button>

        {/* Close Button */}
        <button
          type="button"
          onClick={handleClose}
          className="w-8 h-8 flex items-center justify-center rounded-lg hover:bg-red-500/20 hover:text-red-400 transition-colors duration-200 group"
          title="Close"
        >
          <svg className="w-4 h-4 text-slate-400 group-hover:text-red-400 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* Context Menu */}
      {contextMenu && (
        <ContextMenu
          x={contextMenu.x}
          y={contextMenu.y}
          onClose={() => setContextMenu(null)}
        />
      )}
    </div>
  )
}
