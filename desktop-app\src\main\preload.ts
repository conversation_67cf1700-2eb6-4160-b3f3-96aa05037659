import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getVersion: () => ipcRenderer.invoke('app-version'),
  getPlatform: () => ipcRenderer.invoke('platform'),

  // Menu actions
  onMenuAction: (callback: (action: string) => void) => {
    ipcRenderer.on('menu-action', (_, action) => callback(action))
  },

  // Remove listeners
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel)
  },

  // File operations
  showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
  writeFile: (filePath: string, data: string) => ipcRenderer.invoke('write-file', filePath, data),

  // Notifications
  showNotification: (title: string, body: string) => {
    ipc<PERSON><PERSON>er.invoke('show-notification', title, body)
  }
})

// Type definitions for the exposed API
declare global {
  interface Window {
    electronAPI: {
      getVersion: () => Promise<string>
      getPlatform: () => Promise<string>
      onMenuAction: (callback: (action: string) => void) => void
      removeAllListeners: (channel: string) => void
      showSaveDialog: (options: any) => Promise<any>
      writeFile: (filePath: string, data: string) => Promise<void>
      showNotification: (title: string, body: string) => Promise<void>
      minimizeWindow: () => Promise<void>
      maximizeWindow: () => Promise<void>
      unmaximizeWindow: () => Promise<void>
      closeWindow: () => Promise<void>
      isWindowMaximized: () => Promise<boolean>
      onWindowMaximized: (callback: () => void) => void
      onWindowUnmaximized: (callback: () => void) => void
    }
  }
}

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  getVersion: () => ipcRenderer.invoke('get-version'),
  getPlatform: () => ipcRenderer.invoke('get-platform'),
  onMenuAction: (callback: (action: string) => void) => ipcRenderer.on('menu-action', (_event, action) => callback(action)),
  removeAllListeners: (channel: string) => ipcRenderer.removeAllListeners(channel),
  showSaveDialog: (options: any) => ipcRenderer.invoke('show-save-dialog', options),
  writeFile: (filePath: string, data: string) => ipcRenderer.invoke('write-file', filePath, data),
  showNotification: (title: string, body: string) => ipcRenderer.invoke('show-notification', title, body),
  minimizeWindow: () => ipcRenderer.invoke('window-minimize'),
  maximizeWindow: () => ipcRenderer.invoke('window-maximize'),
  unmaximizeWindow: () => ipcRenderer.invoke('window-unmaximize'),
  closeWindow: () => ipcRenderer.invoke('window-close'),
  isWindowMaximized: () => ipcRenderer.invoke('window-is-maximized'),
  onWindowMaximized: (callback: () => void) => ipcRenderer.on('window-maximized', callback),
  onWindowUnmaximized: (callback: () => void) => ipcRenderer.on('window-unmaximized', callback),
})
