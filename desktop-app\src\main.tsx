import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)

// Use contextBridge - only if electronAPI is available (in Electron environment)
if (typeof window !== 'undefined' && window.electronAPI) {
  // Handle menu actions from main process
  window.electronAPI.onMenuAction((action: string) => {
    console.log('Menu action received:', action)
    // Handle menu actions here if needed
  })
}
