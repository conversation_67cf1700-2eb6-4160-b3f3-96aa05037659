import crypto from 'crypto';
import { User, UserRole } from './types';
import { FirebaseUser } from 'firebase/auth';

// Enhanced Security Configuration
export interface SecurityConfig {
  encryptionKeyLength?: number;
  sessionExpirationDays?: number;
  maxLoginAttempts?: number;
  saltRounds?: number;
}

// Secure Cryptographic Utilities
class CryptoService {
  private static ALGORITHM = 'aes-256-gcm';
  private static KEY_LENGTH = 32;
  private static IV_LENGTH = 16;

  // Generate a cryptographically secure encryption key
  static generateKey(password: string, salt: Buffer): Buffer {
    return crypto.pbkdf2Sync(
      password, 
      salt, 
      100000,  // High iteration count
      this.KEY_LENGTH, 
      'sha512'
    );
  }

  // Secure encryption with authenticated encryption
  static encrypt(data: string, key: Buffer): string {
    const iv = crypto.randomBytes(this.IV_LENGTH);
    const cipher = crypto.createCipheriv(this.ALGORITHM, key, iv);
    
    let encrypted = cipher.update(data, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag();
    
    return JSON.stringify({
      iv: iv.toString('hex'),
      encrypted,
      authTag: authTag.toString('hex')
    });
  }

  // Secure decryption with integrity checking
  static decrypt(encryptedData: string, key: Buffer): string {
    try {
      const { iv, encrypted, authTag } = JSON.parse(encryptedData);
      
      const decipher = crypto.createDecipheriv(
        this.ALGORITHM, 
        key, 
        Buffer.from(iv, 'hex')
      );
      decipher.setAuthTag(Buffer.from(authTag, 'hex'));
      
      let decrypted = decipher.update(encrypted, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      throw new Error('Data integrity compromised');
    }
  }

  // Generate a secure random salt
  static generateSalt(length: number = 16): Buffer {
    return crypto.randomBytes(length);
  }

  // Secure password hashing
  static hashPassword(password: string, salt?: Buffer): { 
    hash: string, 
    salt: Buffer 
  } {
    salt = salt || this.generateSalt();
    const hash = crypto.pbkdf2Sync(
      password, 
      salt, 
      100000,  // High iteration count
      64,      // Longer hash
      'sha512'
    ).toString('hex');
    
    return { hash, salt };
  }

  // Verify password
  static verifyPassword(
    storedHash: string, 
    storedSalt: Buffer, 
    providedPassword: string
  ): boolean {
    const { hash } = this.hashPassword(providedPassword, storedSalt);
    return crypto.timingSafeEqual(
      Buffer.from(storedHash, 'hex'), 
      Buffer.from(hash, 'hex')
    );
  }
}

// Enhanced Secure Storage with Advanced Encryption
class SecureStorage {
  private masterKey: Buffer;
  private salt: Buffer;

  constructor(masterPassword: string) {
    // Generate a persistent salt
    this.salt = CryptoService.generateSalt();
    this.masterKey = CryptoService.generateKey(masterPassword, this.salt);
  }

  // Secure storage with encryption
  set(key: string, value: any): void {
    try {
      const serializedValue = JSON.stringify(value);
      const encryptedValue = CryptoService.encrypt(serializedValue, this.masterKey);
      localStorage.setItem(key, encryptedValue);
    } catch (error) {
      console.error('Secure storage set failed:', error);
      throw new Error('Failed to store encrypted data');
    }
  }

  // Secure retrieval with decryption
  get(key: string): any {
    try {
      const encryptedValue = localStorage.getItem(key);
      if (!encryptedValue) return null;
      
      const decryptedValue = CryptoService.decrypt(encryptedValue, this.masterKey);
      return JSON.parse(decryptedValue);
    } catch (error) {
      console.error('Secure storage get failed:', error);
      return null;
    }
  }

  delete(key: string): void {
    localStorage.removeItem(key);
  }
}

// Enhanced Offline Authentication Service
export class OfflineAuthService {
  private storage: SecureStorage;
  private config: SecurityConfig;
  
  private readonly SESSION_KEY = 'cached_user_session';
  private readonly LOGIN_ATTEMPTS_KEY = 'login_attempts';
  
  constructor(config: SecurityConfig = {}) {
    this.config = {
      encryptionKeyLength: config.encryptionKeyLength || 32,
      sessionExpirationDays: config.sessionExpirationDays || 7,
      maxLoginAttempts: config.maxLoginAttempts || 5,
      saltRounds: config.saltRounds || 100000
    };

    // Initialize secure storage with a complex master password
    this.storage = new SecureStorage(
      this.generateMasterPassword()
    );
  }

  // Generate a complex master password
  private generateMasterPassword(): string {
    return crypto.randomBytes(64).toString('hex');
  }

  // Secure session caching
  async cacheUserSession(
    user: User, 
    firebaseUser: FirebaseUser, 
    password?: string
  ): Promise<void> {
    try {
      // Hash and salt the password if provided
      const passwordData = password 
        ? CryptoService.hashPassword(password) 
        : null;

      const sessionData = {
        user,
        firebaseUid: firebaseUser.uid,
        createdAt: new Date().toISOString(),
        expiresAt: new Date(
          Date.now() + 
          (this.config.sessionExpirationDays! * 24 * 60 * 60 * 1000)
        ).toISOString(),
        passwordHash: passwordData?.hash,
        passwordSalt: passwordData?.salt.toString('hex')
      };

      // Encrypt and store the session
      this.storage.set(this.SESSION_KEY, sessionData);
    } catch (error) {
      console.error('Session caching failed:', error);
      throw new Error('Failed to cache user session');
    }
  }

  // Advanced offline authentication
  async authenticateOffline(
    email: string, 
    password?: string
  ): Promise<User | null> {
    try {
      const session = this.storage.get(this.SESSION_KEY);
      
      // Validate session existence and integrity
      if (!session || !this.isSessionValid(session)) {
        return null;
      }

      // Email validation
      if (session.user.email !== email) {
        return null;
      }

      // Password validation if provided
      if (password && session.passwordHash) {
        const isPasswordValid = CryptoService.verifyPassword(
          session.passwordHash,
          Buffer.from(session.passwordSalt, 'hex'),
          password
        );

        if (!isPasswordValid) {
          this.incrementLoginAttempts();
          return null;
        }
      }

      // Reset login attempts on successful authentication
      this.resetLoginAttempts();

      return session.user;
    } catch (error) {
      console.error('Offline authentication failed:', error);
      return null;
    }
  }

  // Check session validity
  private isSessionValid(session: any): boolean {
    if (!session) return false;

    const expirationDate = new Date(session.expiresAt);
    return expirationDate > new Date();
  }

  // Login attempt tracking
  private incrementLoginAttempts(): void {
    const attempts = this.getLoginAttempts();
    if (attempts >= this.config.maxLoginAttempts!) {
      // Implement account lockout mechanism
      this.lockAccount();
    }
    this.storage.set(this.LOGIN_ATTEMPTS_KEY, attempts + 1);
  }

  private getLoginAttempts(): number {
    return this.storage.get(this.LOGIN_ATTEMPTS_KEY) || 0;
  }

  private resetLoginAttempts(): void {
    this.storage.set(this.LOGIN_ATTEMPTS_KEY, 0);
  }

  // Account lockout mechanism
  private lockAccount(): void {
    // Implement account lockout logic
    // Could involve setting a lockout timestamp
    // or sending alerts
    console.warn('Multiple failed login attempts. Account temporarily locked.');
  }

  // Determine if online authentication is required
  async requiresOnlineAuth(): Promise<boolean> {
    const session = this.storage.get(this.SESSION_KEY);
    
    // Require online auth if:
    // 1. No session exists
    // 2. Session is expired
    // 3. User role requires additional verification
    return !session || 
           !this.isSessionValid(session) || 
           this.needsStrictOnlineAuth(session.user.role);
  }

  // Roles that always require online authentication
  private needsStrictOnlineAuth(role: UserRole): boolean {
    const strictRoles: UserRole[] = ['admin'];
    return strictRoles.includes(role);
  }
}

// Singleton instance management
let offlineAuthInstance: OfflineAuthService | null = null;

export function getOfflineAuthService(
  config?: SecurityConfig
): OfflineAuthService {
  if (!offlineAuthInstance) {
    offlineAuthInstance = new OfflineAuthService(config);
  }
  return offlineAuthInstance;
}
