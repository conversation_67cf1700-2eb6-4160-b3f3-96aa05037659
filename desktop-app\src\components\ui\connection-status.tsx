import React, { useState } from 'react';
import { Wifi, WifiOff, RefreshCw, Database, Clock, AlertCircle, CheckCircle } from 'lucide-react';
import { useOfflineManager } from '../../lib/offline-manager';
import { useAuth } from '../../hooks/use-auth';

interface ConnectionStatusProps {
  className?: string;
  showDetails?: boolean;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ 
  className = '', 
  showDetails = false 
}) => {
  const { status, isLoading, performSync, testConnection } = useOfflineManager();
  const { isOnline, authMode, canWorkOffline, offlineTimeRemaining } = useAuth();
  const [isSyncing, setIsSyncing] = useState(false);
  const [lastSyncResult, setLastSyncResult] = useState<string | null>(null);

  const handleManualSync = async () => {
    if (!isOnline || isSyncing) return;

    setIsSyncing(true);
    setLastSyncResult(null);

    try {
      const result = await performSync();
      if (result.success) {
        setLastSyncResult(`✅ Synced: ${result.totalProcessed} items`);
      } else {
        setLastSyncResult(`❌ Sync failed: ${result.errors.length} errors`);
      }
    } catch (error) {
      setLastSyncResult(`❌ Sync error: ${error.message}`);
    } finally {
      setIsSyncing(false);
      // Clear result after 5 seconds
      setTimeout(() => setLastSyncResult(null), 5000);
    }
  };

  const formatTimeRemaining = (milliseconds: number): string => {
    if (milliseconds <= 0) return '0h';
    
    const hours = Math.floor(milliseconds / (1000 * 60 * 60));
    const days = Math.floor(hours / 24);
    
    if (days > 0) {
      return `${days}d ${hours % 24}h`;
    }
    return `${hours}h`;
  };

  const getStatusIcon = () => {
    if (isLoading) {
      return <RefreshCw className="w-4 h-4 animate-spin text-gray-500" />;
    }

    if (isOnline) {
      if (status?.isSyncing || isSyncing) {
        return <RefreshCw className="w-4 h-4 animate-spin text-blue-500" />;
      }
      return <Wifi className="w-4 h-4 text-green-500" />;
    } else {
      if (canWorkOffline) {
        return <Database className="w-4 h-4 text-yellow-500" />;
      } else {
        return <WifiOff className="w-4 h-4 text-red-500" />;
      }
    }
  };

  const getStatusText = () => {
    if (isLoading) return 'Connecting...';

    if (isOnline) {
      if (status?.isSyncing || isSyncing) return 'Syncing...';
      return 'Online';
    } else {
      if (canWorkOffline) {
        return `Offline (${formatTimeRemaining(offlineTimeRemaining)} left)`;
      } else {
        return 'Offline - Auth Required';
      }
    }
  };

  const getStatusColor = () => {
    if (isLoading) return 'text-gray-500';
    if (isOnline) return 'text-green-600';
    if (canWorkOffline) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (!showDetails) {
    // Compact status indicator
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {getStatusIcon()}
        <span className={`text-sm font-medium ${getStatusColor()}`}>
          {getStatusText()}
        </span>
        {lastSyncResult && (
          <span className="text-xs text-gray-500">{lastSyncResult}</span>
        )}
      </div>
    );
  }

  // Detailed status panel
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 shadow-sm ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        
        {isOnline && !isSyncing && (
          <button
            onClick={handleManualSync}
            className="flex items-center space-x-1 px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors"
            disabled={isSyncing}
          >
            <RefreshCw className="w-3 h-3" />
            <span>Sync</span>
          </button>
        )}
      </div>

      {/* Status Details */}
      <div className="space-y-2 text-sm text-gray-600">
        <div className="flex items-center justify-between">
          <span>Network:</span>
          <div className="flex items-center space-x-1">
            {isOnline ? (
              <>
                <CheckCircle className="w-3 h-3 text-green-500" />
                <span className="text-green-600">Connected</span>
              </>
            ) : (
              <>
                <AlertCircle className="w-3 h-3 text-red-500" />
                <span className="text-red-600">Disconnected</span>
              </>
            )}
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span>Database:</span>
          <div className="flex items-center space-x-1">
            <Database className="w-3 h-3 text-blue-500" />
            <span className="text-blue-600">MongoDB</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <span>Auth Mode:</span>
          <span className={`capitalize ${authMode === 'online' ? 'text-green-600' : 'text-yellow-600'}`}>
            {authMode}
          </span>
        </div>

        {!isOnline && canWorkOffline && (
          <div className="flex items-center justify-between">
            <span>Offline Time:</span>
            <div className="flex items-center space-x-1">
              <Clock className="w-3 h-3 text-yellow-500" />
              <span className="text-yellow-600">
                {formatTimeRemaining(offlineTimeRemaining)}
              </span>
            </div>
          </div>
        )}

        {status?.lastSyncTime && (
          <div className="flex items-center justify-between">
            <span>Last Sync:</span>
            <span className="text-gray-500">
              {status.lastSyncTime.toLocaleTimeString()}
            </span>
          </div>
        )}

        {lastSyncResult && (
          <div className="mt-2 p-2 bg-gray-50 rounded text-xs">
            {lastSyncResult}
          </div>
        )}
      </div>

      {/* Warning Messages */}
      {!isOnline && !canWorkOffline && (
        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-700">
              Please connect to internet to authenticate
            </span>
          </div>
        </div>
      )}

      {!isOnline && canWorkOffline && offlineTimeRemaining < 24 * 60 * 60 * 1000 && (
        <div className="mt-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center space-x-2">
            <Clock className="w-4 h-4 text-yellow-500" />
            <span className="text-sm text-yellow-700">
              Offline access expires soon. Connect to internet to extend.
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

// Hook for connection status
export const useConnectionStatus = () => {
  const { status, isLoading } = useOfflineManager();
  const { isOnline, authMode, canWorkOffline, offlineTimeRemaining } = useAuth();

  return {
    isOnline,
    isOffline: !isOnline,
    authMode,
    canWorkOffline,
    offlineTimeRemaining,
    isSyncing: status?.isSyncing || false,
    lastSyncTime: status?.lastSyncTime || null,
    isLoading,
    status: isOnline ? 'online' : canWorkOffline ? 'offline' : 'disconnected'
  };
};

export default ConnectionStatus;
